'use client';

import { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, Environment, Text, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

interface SkillData {
  name: string;
  level: number;
  icon: React.ReactNode;
  position: [number, number, number];
  color: string;
}

interface GlitchTextProps {
  text: string;
  position: [number, number, number];
  color: string;
  isVisible: boolean;
  delay: number;
}

function GlitchText({ text, position, color, isVisible, delay }: GlitchTextProps) {
  const [displayText, setDisplayText] = useState('');
  const [isGlitching, setIsGlitching] = useState(false);
  const textRef = useRef<any>(null);

  const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?~`';

  useEffect(() => {
    if (!isVisible) return;

    const timeout = setTimeout(() => {
      setIsGlitching(true);
      let currentIndex = 0;

      const glitchInterval = setInterval(() => {
        if (currentIndex < text.length) {
          // Glitch effect for current character
          const glitchText = text.split('').map((char, index) => {
            if (index < currentIndex) return char;
            if (index === currentIndex) {
              return Math.random() > 0.7 ? char : glitchChars[Math.floor(Math.random() * glitchChars.length)];
            }
            return glitchChars[Math.floor(Math.random() * glitchChars.length)];
          }).join('');

          setDisplayText(glitchText);

          // Settle the current character after some glitching
          if (Math.random() > 0.3) {
            currentIndex++;
          }
        } else {
          setDisplayText(text);
          setIsGlitching(false);
          clearInterval(glitchInterval);
        }
      }, 50);

      return () => clearInterval(glitchInterval);
    }, delay);

    return () => clearTimeout(timeout);
  }, [isVisible, text, delay]);

  useFrame((state) => {
    if (textRef.current && isVisible) {
      // Floating animation
      textRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + delay) * 0.1;

      // Subtle glow effect - check if material exists and has emissive property
      if (textRef.current.material && textRef.current.material.emissive) {
        if (isGlitching) {
          textRef.current.material.emissive.setHex(0x444444);
        } else {
          textRef.current.material.emissive.setHex(0x000000);
        }
      }
    }
  });

  if (!isVisible) return null;

  return (
    <Text
      ref={textRef}
      position={position}
      fontSize={0.35}
      color="#ffffff"
      anchorX="center"
      anchorY="middle"
      outlineWidth={0.03}
      outlineColor="#6666ff"
      material-toneMapped={false}
    >
      {displayText}
    </Text>
  );
}

interface Interactive3DModelProps {
  modelPath: string;
  skills: SkillData[];
}

function Interactive3DModel({ modelPath, skills }: Interactive3DModelProps) {
  const modelRef = useRef<THREE.Group>(null);
  const [skillsVisible, setSkillsVisible] = useState(false);
  const [modelLoaded, setModelLoaded] = useState(false);

  // Use useGLTF hook with error handling
  let gltf;
  try {
    gltf = useGLTF(modelPath);
    if (gltf && gltf.scene) {
      console.log('✅ GLTF loaded successfully:', modelPath, gltf);
    } else {
      console.log('⚠️ GLTF loaded but no scene:', gltf);
    }
  } catch (error) {
    console.error('❌ Error loading GLTF:', error);
  }

  // Enhanced safety check for GLTF loading
  if (!gltf || !gltf.scene) {
    console.log('GLTF not loaded, showing fallback');
    return (
      <group>
        {/* Fallback 3D object - more interesting than a box */}
        <mesh position={[0, 0, 0]}>
          <icosahedronGeometry args={[2, 1]} />
          <meshStandardMaterial
            color="#6666ff"
            wireframe
            transparent
            opacity={0.8}
          />
        </mesh>

        {/* Orbital rings as fallback */}
        {[2.5, 3.5, 4.5].map((radius, index) => (
          <mesh key={index} rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
            <ringGeometry args={[radius - 0.1, radius, 32]} />
            <meshBasicMaterial
              color="#6666ff"
              transparent
              opacity={0.3 - index * 0.05}
              side={THREE.DoubleSide}
            />
          </mesh>
        ))}

        {/* Show skills even if model fails */}
        {skills.map((skill, index) => (
          <group key={skill.name}>
            <mesh position={skill.position}>
              <sphereGeometry args={[0.15, 16, 16]} />
              <meshStandardMaterial color={skill.color} emissive={skill.color} emissiveIntensity={0.2} />
            </mesh>
          </group>
        ))}
      </group>
    );
  }

  const { scene } = gltf;

  useEffect(() => {
    if (scene) {
      setModelLoaded(true);
      console.log('3D Model loaded and ready');
    }
  }, [scene]);

  useFrame((state) => {
    if (modelRef.current) {
      // Slow rotation - stable during transitions
      modelRef.current.rotation.y += 0.005;

      // Breathing animation - reduced scale, more stable
      const breathe = 2.0 + Math.sin(state.clock.elapsedTime * 0.6) * 0.1;
      modelRef.current.scale.setScalar(breathe);

      // Mouse interaction - add safety checks and reduce intensity for stability
      if (state.mouse) {
        modelRef.current.rotation.x = state.mouse.y * 0.1;
        modelRef.current.rotation.z = state.mouse.x * 0.05;
      }
    }
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setSkillsVisible(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <group>
      {/* Main 3D Model - Centered and Prominent */}
      <group ref={modelRef} position={[0, -0.5, 0]} scale={1}>
        <primitive object={scene} />
      </group>

      {/* Floating Skill Labels */}
      {skills.map((skill, index) => (
        <group key={skill.name}>
          <GlitchText
            text={skill.name.toUpperCase()}
            position={skill.position}
            color={skill.color}
            isVisible={skillsVisible}
            delay={index * 500}
          />

          {/* Skill Level Bar */}
          {skillsVisible && (
            <mesh position={[skill.position[0], skill.position[1] - 0.4, skill.position[2]]}>
              <boxGeometry args={[1, 0.1, 0.02]} />
              <meshBasicMaterial color="#333333" transparent opacity={0.8} />
            </mesh>
          )}

          {/* Skill Level Fill */}
          {skillsVisible && (
            <mesh position={[skill.position[0] - 0.5 + (skill.level / 100) * 0.5, skill.position[1] - 0.4, skill.position[2] + 0.01]}>
              <boxGeometry args={[(skill.level / 100), 0.1, 0.02]} />
              <meshBasicMaterial color={skill.color} />
            </mesh>
          )}
        </group>
      ))}

      {/* Orbital Rings */}
      <group>
        {[2, 3, 4].map((radius, index) => (
          <mesh key={index} rotation={[Math.PI / 2, 0, 0]} position={[0, -1, 0]}>
            <ringGeometry args={[radius - 0.05, radius, 64]} />
            <meshBasicMaterial
              color="#6666ff"
              transparent
              opacity={0.1 - index * 0.02}
              side={THREE.DoubleSide}
            />
          </mesh>
        ))}
      </group>
    </group>
  );
}

interface Interactive3DSkillsProps {
  modelPath: string;
  skills: SkillData[];
  className?: string;
}

export default function Interactive3DSkills({
  modelPath,
  skills,
  className = "w-full h-full"
}: Interactive3DSkillsProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Reset error state when props change
    setHasError(false);
  }, [modelPath, skills]);

  if (hasError) {
    return (
      <div className={`${className} flex items-center justify-center bg-gradient-to-br from-[#6666ff]/10 to-[#0044cc]/10 rounded-lg`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-[#6666ff] rounded-full flex items-center justify-center">
            <span className="text-[#6666ff] text-2xl">⚠</span>
          </div>
          <p className="text-black font-semibold mb-4">3D Model Loading Error</p>
          <button
            onClick={() => setHasError(false)}
            className="px-6 py-2 bg-[#6666ff] text-black font-semibold rounded-lg hover:bg-[#0044cc] transition-colors"
          >
            Retry Loading
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={{ background: 'transparent' }}>
      <Canvas
        camera={{ position: [0, 0, 8], fov: 50 }}
        style={{
          background: 'transparent',
          width: '100%',
          height: '100%'
        }}
        dpr={[1, 2]}
        gl={{
          alpha: true,
          antialias: true,
          preserveDrawingBuffer: true
        }}
        onError={(error) => {
          console.error('Canvas error:', error);
          setHasError(true);
        }}
        onCreated={({ gl, scene, camera }) => {
          // Set transparent background
          gl.setClearColor(0x000000, 0);
          scene.background = null;

          // Adjust camera for better model visibility
          camera.position.set(0, 0, 8);
          camera.lookAt(0, 0, 0);

          console.log('✅ Canvas created with transparent background');
          console.log('Camera position:', camera.position);
          console.log('WebGL context:', gl.getContextAttributes());
        }}
      >
        {/* Enhanced Gaming-style Lighting */}
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1.5} color="#6666ff" />
        <directionalLight position={[-10, -10, -5]} intensity={1.0} color="#0044cc" />
        <pointLight position={[0, 0, 10]} intensity={1.2} color="#ffffff" />
        <spotLight
          position={[0, 10, 0]}
          angle={0.3}
          penumbra={1}
          intensity={0.8}
          color="#6666ff"
        />

        {/* Environment */}
        <Environment preset="night" environmentIntensity={0.4} />

        {/* Interactive Controls */}
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          enableRotate={true}
          autoRotate={false}
          rotateSpeed={0.5}
        />

        {/* Interactive 3D Model with Skills */}
        <Suspense fallback={
          <group>
            {/* Enhanced fallback with neural network appearance */}
            <mesh position={[0, 0, 0]}>
              <icosahedronGeometry args={[2, 1]} />
              <meshStandardMaterial
                color="#6666ff"
                wireframe
                transparent
                opacity={0.8}
              />
            </mesh>

            {/* Orbital rings */}
            {[2.5, 3.5, 4.5].map((radius, index) => (
              <mesh key={index} rotation={[Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
                <ringGeometry args={[radius - 0.1, radius, 32]} />
                <meshBasicMaterial
                  color="#6666ff"
                  transparent
                  opacity={0.3 - index * 0.05}
                  side={THREE.DoubleSide}
                />
              </mesh>
            ))}

            {/* Pulsing core */}
            <mesh position={[0, 0, 0]}>
              <sphereGeometry args={[0.5, 16, 16]} />
              <meshStandardMaterial
                color="#6666ff"
                emissive="#6666ff"
                emissiveIntensity={0.5}
              />
            </mesh>
          </group>
        }>
          <Interactive3DModel modelPath={modelPath} skills={skills} />
        </Suspense>
      </Canvas>
    </div>
  );
}

// Preload function
export function preloadInteractive3D(modelPath: string) {
  useGLTF.preload(modelPath);
}
