'use client';

import { useState, useEffect } from 'react';
import { FaPython, FaRobot, FaBrain, FaChartLine, FaCode, FaDatabase } from 'react-icons/fa';
import dynamic from 'next/dynamic';
import ScrollTransition from './ScrollTransition';

// Dynamically import the interactive 3D component
const Interactive3DSkills = dynamic(() => import('./Interactive3DSkills'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
    </div>
  )
});

// Dynamically import the Projects component for the transition
const ProjectsSection = dynamic(() => import('./ProjectsSection'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 flex items-center justify-center">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
    </div>
  )
});

// Preload the model for better performance
if (typeof window !== 'undefined') {
  import('./Interactive3DSkills').then(({ preloadInteractive3D }) => {
    preloadInteractive3D('/a_windy_day.glb');
  });
}

export default function AboutSection() {
  const [expanded, setExpanded] = useState(false);
  const [scrollOffset, setScrollOffset] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const aboutSection = document.getElementById('about');
      if (aboutSection) {
        const rect = aboutSection.getBoundingClientRect();
        const offset = (window.innerHeight - rect.top) / window.innerHeight;
        setScrollOffset(offset);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMounted]);

  // Gaming-style skills data with 3D positioning
  const skills3D = [
    {
      name: 'Python',
      level: 95,
      icon: <FaPython className="text-4xl text-blue-500" />,
      position: [-2, 1.5, 0.5] as [number, number, number],
      color: '#0044cc'
    },
    {
      name: 'TensorFlow',
      level: 90,
      icon: <FaRobot className="text-4xl text-orange-500" />,
      position: [2, 1, -0.5] as [number, number, number],
      color: '#0044cc'
    },
    {
      name: 'PyTorch',
      level: 85,
      icon: <FaBrain className="text-4xl text-red-500" />,
      position: [-1.5, -0.3, 1.2] as [number, number, number],
      color: '#0044cc'
    },
    {
      name: 'Scikit-Learn',
      level: 80,
      icon: <FaChartLine className="text-4xl text-green-500" />,
      position: [1.8, -0.7, 1] as [number, number, number],
      color: '#0044cc'
    },
    {
      name: 'Deep Learning',
      level: 88,
      icon: <FaBrain className="text-4xl text-indigo-500" />,
      position: [0, 1.8, -1.2] as [number, number, number],
      color: '#0044cc'
    },
    {
      name: 'SQL/NoSQL',
      level: 85,
      icon: <FaDatabase className="text-4xl text-yellow-500" />,
      position: [-2.2, 0, -0.7] as [number, number, number],
      color: '#0044cc'
    }
  ];

  if (!isMounted) {
    return (
      <section id="about" className="py-20 relative min-h-screen">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white font-mono tracking-wider drop-shadow-lg">
              NEURAL INTERFACE
            </h2>
            <p className="text-lg text-white mt-4 font-semibold font-mono tracking-wide">AI/ML Engineer • Skills Matrix Loading...</p>
          </div>
          <div className="flex items-center justify-center h-96">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#6666ff]"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <ScrollTransition
      nextSection={<ProjectsSection />}
      className="min-h-screen"
    >
      <section id="about" className="py-20 relative min-h-screen overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white font-mono tracking-wider drop-shadow-lg" style={{
              fontFamily: 'Courier New, monospace',
              textShadow: '0 0 10px #6666ff, 0 0 20px #6666ff, 0 0 30px #6666ff'
            }}>
              NEURAL INTERFACE
            </h2>
            <p className="text-lg text-white mt-4 font-semibold font-mono tracking-wide">AI/ML Engineer • Skills Matrix Loading...</p>
          </div>

        {/* Gaming-style layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 relative z-10">
          {/* Bio Panel - Reduced width */}
          <div className="lg:col-span-1">
            <div className="gaming-ui-container p-4 h-full">
              <h3 className="text-lg font-bold text-white font-mono tracking-wide mb-4" style={{
                textShadow: '0 0 5px #6666ff'
              }}>OPERATOR PROFILE</h3>
              <div className="space-y-4 text-white font-mono">
                <div className="skill-stat-box">
                  <span className="text-[#6666ff] font-semibold">STATUS:</span> ACTIVE
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#6666ff] font-semibold">EXPERIENCE:</span> 5+ YEARS
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#6666ff] font-semibold">SPECIALIZATION:</span> AI/ML SYSTEMS
                </div>
                <div className="skill-stat-box">
                  <span className="text-[#6666ff] font-semibold">CLEARANCE:</span> NEURAL NETWORKS
                </div>
              </div>

              {expanded ? (
                <div className="mt-6 space-y-4 text-white font-mono">
                  <div className="skill-stat-box">
                    <span className="text-[#6666ff] font-semibold">EDUCATION:</span> MS Computer Science
                  </div>
                  <div className="skill-stat-box">
                    <span className="text-[#6666ff] font-semibold">RESEARCH:</span> Transformer Architectures
                  </div>
                  <div className="skill-stat-box">
                    <span className="text-[#6666ff] font-semibold">IMPACT:</span> 40% Efficiency Boost
                  </div>
                  <button
                    onClick={() => setExpanded(false)}
                    className="skill-stat-box hover:bg-[#6666ff]/20 cursor-pointer transition-all duration-300 text-white font-mono"
                  >
                    <span className="text-[#ff6666]">MINIMIZE DATA</span>
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setExpanded(true)}
                  className="mt-6 skill-stat-box hover:bg-[#6666ff]/20 cursor-pointer transition-all duration-300 w-full text-white font-mono"
                >
                  <span className="text-[#6666ff] font-semibold">EXPAND PROFILE</span>
                </button>
              )}
            </div>
          </div>

          {/* 3D Interactive Skills Display - Increased width */}
          <div className="lg:col-span-3">
            <div className="gaming-ui-container h-[500px] lg:h-[700px] relative overflow-hidden">
              <div className="absolute top-4 left-4 z-20">
                <h3 className="text-xl font-bold text-white font-mono tracking-wide" style={{
                  textShadow: '0 0 8px #6666ff'
                }}>SKILLS MATRIX</h3>
                <p className="text-sm text-white font-mono tracking-wide mt-1">Interactive 3D Neural Network</p>
              </div>

              <Interactive3DSkills
                modelPath="/a_windy_day.glb"
                skills={skills3D}
                className="w-full h-full"
              />

              <div className="absolute bottom-4 right-4 z-20">
                <div className="text-xs text-white font-mono tracking-wide">
                  <p>MOUSE: ROTATE • AUTO-SPIN</p>
                  <p>STATUS: <span className="text-[#6666ff] font-semibold">ONLINE</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </ScrollTransition>
  );
}