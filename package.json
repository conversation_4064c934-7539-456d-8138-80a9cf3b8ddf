{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}