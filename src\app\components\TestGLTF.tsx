'use client';

import { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';

function TestModel() {
  console.log('🔍 TestModel component rendering...');
  
  try {
    const gltf = useGLTF('/a_windy_day.glb');
    console.log('🔍 GLTF result:', gltf);
    
    if (gltf && gltf.scene) {
      console.log('✅ GLTF loaded successfully in test');
      return <primitive object={gltf.scene} scale={0.5} />;
    } else {
      console.log('❌ GLTF scene not available');
      return (
        <mesh>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="red" />
        </mesh>
      );
    }
  } catch (error) {
    console.error('❌ Error in TestModel:', error);
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="orange" />
      </mesh>
    );
  }
}

export default function TestGLTF() {
  return (
    <div style={{ width: '400px', height: '400px', background: '#333' }}>
      <Canvas camera={{ position: [0, 0, 5] }}>
        <ambientLight intensity={0.5} />
        <directionalLight position={[10, 10, 5]} intensity={1} />
        
        <Suspense fallback={
          <mesh>
            <boxGeometry args={[1, 1, 1]} />
            <meshStandardMaterial color="blue" />
          </mesh>
        }>
          <TestModel />
        </Suspense>
      </Canvas>
    </div>
  );
}
