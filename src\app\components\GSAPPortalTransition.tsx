'use client';

import { useEffect, useRef, ReactNode } from 'react';

// Dynamic import for GSAP to avoid SSR issues
let gsap: any;
let ScrollTrigger: any;

if (typeof window !== 'undefined') {
  import('gsap').then((gsapModule) => {
    gsap = gsapModule.gsap;
    return import('gsap/ScrollTrigger');
  }).then((scrollTriggerModule) => {
    ScrollTrigger = scrollTriggerModule.ScrollTrigger;
    gsap.registerPlugin(ScrollTrigger);
  });
}

interface GSAPPortalTransitionProps {
  children: ReactNode;
  nextSections: ReactNode;
  className?: string;
}

export default function GSAPPortalTransition({
  children,
  nextSections,
  className = ""
}: GSAPPortalTransitionProps) {
  const portalRef = useRef<HTMLDivElement>(null);
  const nextSectionsRef = useRef<HTMLDivElement>(null);
  const wormholeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!portalRef.current || !nextSectionsRef.current || !wormholeRef.current) return;

    const portal = portalRef.current;
    const nextSections = nextSectionsRef.current;
    const wormhole = wormholeRef.current;

    // Initialize GSAP animations
    const initGSAP = async () => {
      if (typeof window === 'undefined') return;

      try {
        const gsapModule = await import('gsap');
        const scrollTriggerModule = await import('gsap/ScrollTrigger');

        const gsap = gsapModule.gsap;
        const ScrollTrigger = scrollTriggerModule.ScrollTrigger;

        gsap.registerPlugin(ScrollTrigger);

        // Create the main timeline
        const tl = gsap.timeline({
          scrollTrigger: {
            trigger: portal,
            start: "bottom bottom",
            end: () => "+=" + window.innerHeight * 3, // 3 viewport heights of scroll
            pin: true,
            scrub: 1,
            anticipatePin: 1,
            onUpdate: (self) => {
              console.log('Portal scroll progress:', self.progress);
            }
          }
        });

        // Phase 1: Portal expansion (0-30% of scroll)
        tl.fromTo(portal,
          {
            scale: 1,
            borderRadius: "15px",
            rotationX: 0,
            rotationY: 0
          },
          {
            scale: 1.1,
            borderRadius: "8px",
            duration: 0.2,
            ease: "power2.out"
          }
        )
        // Phase 2: Dramatic scaling and rotation (30-60% of scroll)
        .to(portal, {
          scale: 1.5,
          borderRadius: "0px",
          rotationX: 5,
          rotationY: 2,
          duration: 0.2,
          ease: "power2.inOut"
        })
        .to(portal, {
          scale: 3,
          rotationX: 15,
          rotationY: 8,
          opacity: 0.7,
          duration: 0.2,
          ease: "power3.in"
        })
        // Phase 3: Final portal dissolution (60-70% of scroll)
        .to(portal, {
          scale: 6,
          rotationX: 45,
          rotationY: 25,
          opacity: 0.1,
          duration: 0.1,
          ease: "power4.in"
        });

        // Phase 2: Wormhole visual effects (starts at 20% scroll)
        tl.fromTo(wormhole,
          {
            opacity: 0,
            scale: 0.3,
            rotation: 0
          },
          {
            opacity: 0.8,
            scale: 1.5,
            rotation: 180,
            duration: 0.3,
            ease: "power2.out"
          }, 0.2
        )
        .to(wormhole, {
          scale: 2.5,
          rotation: 360,
          opacity: 1,
          duration: 0.2,
          ease: "power2.inOut"
        })
        .to(wormhole, {
          scale: 4,
          rotation: 540,
          opacity: 0.3,
          duration: 0.2,
          ease: "power3.in"
        });

        // Phase 4: Next sections reveal (70-100% of scroll)
        tl.fromTo(nextSections,
          {
            opacity: 0,
            y: window.innerHeight * 1.5,
            scale: 0.6,
            rotationX: -20
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            rotationX: 0,
            duration: 0.3,
            ease: "power3.out"
          }, 0.7
        );

        // Cleanup function
        return () => {
          ScrollTrigger.getAll().forEach(trigger => trigger.kill());
        };
      } catch (error) {
        console.error('Error initializing GSAP:', error);
      }
    };

    initGSAP();
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Main Portal Section */}
      <div
        ref={portalRef}
        className="portal-section relative min-h-screen overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, rgba(102, 102, 255, 0.15), rgba(0, 0, 0, 0.8))',
          border: '2px solid rgba(102, 102, 255, 0.4)',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 0 30px rgba(102, 102, 255, 0.3), inset 0 0 30px rgba(102, 102, 255, 0.1)'
        }}
      >
        {children}
      </div>

      {/* Wormhole Effect Overlay */}
      <div
        ref={wormholeRef}
        className="fixed inset-0 pointer-events-none z-50"
        style={{
          background: `radial-gradient(circle at center,
            transparent 0%,
            rgba(102, 102, 255, 0.3) 30%,
            rgba(0, 68, 204, 0.6) 60%,
            rgba(0, 0, 0, 0.9) 100%)`,
          opacity: 0
        }}
      >
        {/* Wormhole Tunnel Rings */}
        {Array.from({ length: 15 }, (_, i) => (
          <div
            key={`wormhole-ring-${i}`}
            className="absolute rounded-full"
            style={{
              width: `${60 + i * 80}px`,
              height: `${60 + i * 80}px`,
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              border: `${3 + i * 0.5}px solid rgba(102, 102, 255, ${0.9 - i * 0.05})`,
              boxShadow: `
                0 0 ${15 + i * 8}px rgba(102, 102, 255, ${0.8 - i * 0.04}),
                inset 0 0 ${10 + i * 5}px rgba(0, 68, 204, ${0.6 - i * 0.03})
              `,
              animation: `wormholeRing ${1.5 + i * 0.2}s ease-in-out infinite`,
              animationDelay: `${i * 0.08}s`
            }}
          />
        ))}

        {/* Energy Spiral */}
        {Array.from({ length: 8 }, (_, i) => (
          <div
            key={`energy-spiral-${i}`}
            className="absolute"
            style={{
              width: '200px',
              height: '200px',
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              background: `conic-gradient(from ${i * 45}deg,
                transparent 0deg,
                rgba(102, 102, 255, 0.3) 90deg,
                transparent 180deg)`,
              borderRadius: '50%',
              animation: `energySpiral ${2 + i * 0.3}s linear infinite`,
              animationDelay: `${i * 0.1}s`
            }}
          />
        ))}

        {/* Energy Particles */}
        {Array.from({ length: 30 }, (_, i) => (
          <div
            key={`particle-${i}`}
            className="absolute w-2 h-2 bg-white rounded-full"
            style={{
              left: `${20 + Math.random() * 60}%`,
              top: `${20 + Math.random() * 60}%`,
              background: `radial-gradient(circle,
                rgba(102, 102, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.6) 50%,
                transparent 100%)`,
              animation: `portalParticle ${1.5 + Math.random() * 2}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Next Sections Container */}
      <div
        ref={nextSectionsRef}
        className="next-sections-container relative"
        style={{
          background: 'linear-gradient(180deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 26, 46, 0.9) 20%, rgba(0, 68, 204, 0.1) 50%, rgba(0, 0, 0, 0.95) 100%)',
          minHeight: '100vh'
        }}
      >
        {nextSections}
      </div>
    </div>
  );
}
