'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollTransitionProps {
  children: React.ReactNode;
  nextSection: React.ReactNode;
  className?: string;
}

export default function ScrollTransition({
  children,
  nextSection,
  className = ""
}: ScrollTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Modified scroll configuration to trigger only at the end of the section
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Smooth spring animation for the transition
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Modified transform values - transition only starts when user reaches the end (0.8+)
  const scale = useTransform(smoothProgress, [0, 0.8, 0.85, 0.9, 0.95, 1], [1, 1, 1.2, 1.8, 3, 5]);
  const opacity = useTransform(smoothProgress, [0, 0.8, 0.85, 0.92, 0.98, 1], [1, 1, 0.9, 0.6, 0.2, 0]);
  const rotateX = useTransform(smoothProgress, [0, 0.8, 0.9, 1], [0, 0, 15, 45]);
  const rotateY = useTransform(smoothProgress, [0, 0.8, 0.9, 1], [0, 0, 10, 30]);
  const z = useTransform(smoothProgress, [0, 0.8, 0.85, 0.9, 0.95, 1], [0, 0, 50, 200, 500, 1000]);

  // Background transition effect - only starts at the end
  const backgroundOpacity = useTransform(smoothProgress, [0, 0.8, 0.9, 1], [0, 0, 0.5, 1]);

  // Next section reveal - only when falling through space
  const nextSectionY = useTransform(smoothProgress, [0, 0.8, 0.9, 0.95, 1], [200, 200, 100, 50, 0]);
  const nextSectionOpacity = useTransform(smoothProgress, [0, 0.8, 0.9, 0.95, 1], [0, 0, 0.3, 0.7, 1]);

  // Additional space effects - only during transition
  const blur = useTransform(smoothProgress, [0, 0.8, 0.9, 1], [0, 0, 5, 12]);
  const brightness = useTransform(smoothProgress, [0, 0.8, 0.9, 1], [1, 1, 1.3, 0.4]);

  // Container expansion effect - fills viewport when falling through space
  const containerWidth = useTransform(smoothProgress, [0, 0.8, 0.85, 0.9], ['100%', '100%', '100vw', '100vw']);
  const containerHeight = useTransform(smoothProgress, [0, 0.8, 0.85, 0.9], ['auto', 'auto', '100vh', '100vh']);
  const containerPosition = useTransform(smoothProgress, [0, 0.8, 0.85], ['relative', 'relative', 'fixed']);

  // Create stable star configurations to avoid hydration mismatch
  const starConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 100 }, (_, i) => ({
      id: i,
      size: 1 + (i % 3),
      speed: 1 + (i % 3),
      left: (i * 7.3) % 100,
      top: (i * 11.7) % 100,
      delay: (i * 0.03) % 3,
    }));
  }, [isMounted]);

  const nebulaConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 5 }, (_, i) => ({
      id: i,
      width: 100 + (i * 20),
      height: 100 + (i * 15),
      left: (i * 20) % 80,
      top: (i * 25) % 80,
      delay: i * 0.4,
      duration: 8 + (i * 0.8),
    }));
  }, [isMounted]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const unsubscribe = smoothProgress.on('change', (latest) => {
      // Transition only starts when user reaches the end and tries to scroll further
      setIsTransitioning(latest > 0.8);
    });

    return () => unsubscribe();
  }, [smoothProgress]);

  return (
    <div ref={containerRef} className={`relative scroll-transition-container ${className}`}>
      {/* Main content with transition effects and viewport expansion */}
      <motion.div
        style={{
          scale,
          opacity,
          rotateX,
          rotateY,
          z,
          filter: `blur(${blur}px) brightness(${brightness})`,
          width: containerWidth,
          height: containerHeight,
          position: containerPosition,
          top: 0,
          left: 0,
          zIndex: isTransitioning ? 9999 : 10,
        }}
        className={`relative transform-gpu space-transition ${isTransitioning ? 'expanding' : ''}`}
      >
        {children}
      </motion.div>

      {/* Transition background overlay */}
      <motion.div
        style={{ opacity: backgroundOpacity }}
        className="fixed inset-0 bg-gradient-to-b from-black via-[#1a1a2e] to-[#16213e] z-5 pointer-events-none"
      />

      {/* Enhanced space tunnel effect during transition */}
      {isMounted && isTransitioning && (
        <div className="fixed inset-0 z-5 pointer-events-none">
          {/* Space tunnel rings */}
          {Array.from({ length: 8 }, (_, i) => (
            <motion.div
              key={`tunnel-${i}`}
              className="absolute border-2 border-white/20 rounded-full"
              style={{
                width: `${100 + i * 50}px`,
                height: `${100 + i * 50}px`,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
              }}
              animate={{
                scale: [0.5, 3, 6],
                opacity: [0.8, 0.3, 0],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeOut",
              }}
            />
          ))}

          {/* Fast-moving stars for speed effect */}
          {starConfigs.map((star) => (
            <motion.div
              key={star.id}
              className="absolute bg-white rounded-full"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                left: `${star.left}%`,
                top: `${star.top}%`,
                boxShadow: `0 0 ${star.size * 3}px rgba(255, 255, 255, 0.9)`,
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1.5, 0],
                y: [0, -((typeof window !== 'undefined' ? window.innerHeight : 800) * star.speed * 2)],
                x: [(star.left - 50) * 2, (star.left - 50) * 4],
              }}
              transition={{
                duration: 1.5 / star.speed,
                repeat: Infinity,
                delay: star.delay,
                ease: "easeIn",
              }}
            />
          ))}

          {/* Dimensional portal effect */}
          <motion.div
            className="absolute inset-0"
            style={{
              background: `radial-gradient(circle at center,
                transparent 0%,
                rgba(102, 102, 255, 0.1) 30%,
                rgba(0, 68, 204, 0.2) 60%,
                rgba(0, 0, 0, 0.8) 100%)`,
            }}
            animate={{
              scale: [1, 1.5, 2],
              opacity: [0, 0.5, 1],
            }}
            transition={{
              duration: 2,
              ease: "easeInOut",
            }}
          />

          {/* Nebula-like background effects */}
          {nebulaConfigs.map((nebula) => (
            <motion.div
              key={`nebula-${nebula.id}`}
              className="absolute rounded-full"
              style={{
                width: `${nebula.width}px`,
                height: `${nebula.height}px`,
                left: `${nebula.left}%`,
                top: `${nebula.top}%`,
                background: `radial-gradient(circle, rgba(102, 102, 255, 0.15) 0%, rgba(0, 68, 204, 0.08) 50%, transparent 100%)`,
              }}
              animate={{
                opacity: [0.1, 0.4, 0.1],
                scale: [0.8, 1.5, 0.8],
                rotate: [0, 360],
              }}
              transition={{
                duration: nebula.duration * 0.7,
                repeat: Infinity,
                delay: nebula.delay,
              }}
            />
          ))}
        </div>
      )}

      {/* Next section preview */}
      <motion.div
        style={{
          y: nextSectionY,
          opacity: nextSectionOpacity,
        }}
        className="relative z-20"
      >
        {nextSection}
      </motion.div>
    </div>
  );
}
