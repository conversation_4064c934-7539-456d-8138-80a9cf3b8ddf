'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollTransitionProps {
  children: React.ReactNode;
  nextSection: React.ReactNode;
  className?: string;
}

export default function ScrollTransition({
  children,
  nextSection,
  className = ""
}: ScrollTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Portal scroll configuration - triggers only at the very end
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Smooth spring animation for the portal transition
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 80,
    damping: 25,
    restDelta: 0.001
  });

  // Portal transition - only starts when user tries to scroll past the end (0.9+)
  const scale = useTransform(smoothProgress, [0, 0.9, 0.92, 0.95, 0.98, 1], [1, 1, 1.1, 1.5, 2.5, 4]);
  const opacity = useTransform(smoothProgress, [0, 0.9, 0.93, 0.96, 0.99, 1], [1, 1, 0.95, 0.7, 0.3, 0]);
  const rotateX = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 10, 25]);
  const rotateY = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 5, 15]);
  const z = useTransform(smoothProgress, [0, 0.9, 0.92, 0.95, 0.98, 1], [0, 0, 30, 150, 400, 800]);

  // Wormhole background effect - dramatic portal opening
  const wormholeOpacity = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 0.7, 1]);
  const wormholeScale = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 1, 2]);

  // Portal container expansion - fills viewport during wormhole
  const portalWidth = useTransform(smoothProgress, [0, 0.9, 0.92], ['100%', '100%', '100vw']);
  const portalHeight = useTransform(smoothProgress, [0, 0.9, 0.92], ['auto', 'auto', '100vh']);
  const portalPosition = useTransform(smoothProgress, [0, 0.9, 0.92], ['relative', 'relative', 'fixed']);

  // Next sections reveal - all sections appear as if contained within the portal
  const nextSectionY = useTransform(smoothProgress, [0, 0.9, 0.95, 0.98, 1], [300, 300, 150, 50, 0]);
  const nextSectionOpacity = useTransform(smoothProgress, [0, 0.9, 0.95, 0.98, 1], [0, 0, 0.4, 0.8, 1]);

  // Portal visual effects - only during wormhole transition
  const blur = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [0, 0, 3, 8]);
  const brightness = useTransform(smoothProgress, [0, 0.9, 0.95, 1], [1, 1, 1.4, 0.5]);

  // Create stable star configurations to avoid hydration mismatch
  const starConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 100 }, (_, i) => ({
      id: i,
      size: 1 + (i % 3),
      speed: 1 + (i % 3),
      left: (i * 7.3) % 100,
      top: (i * 11.7) % 100,
      delay: (i * 0.03) % 3,
    }));
  }, [isMounted]);

  const nebulaConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 5 }, (_, i) => ({
      id: i,
      width: 100 + (i * 20),
      height: 100 + (i * 15),
      left: (i * 20) % 80,
      top: (i * 25) % 80,
      delay: i * 0.4,
      duration: 8 + (i * 0.8),
    }));
  }, [isMounted]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const unsubscribe = smoothProgress.on('change', (latest) => {
      // Portal transition only starts when user reaches the very end (0.9+)
      setIsTransitioning(latest > 0.9);
    });

    return () => unsubscribe();
  }, [smoothProgress]);

  return (
    <div ref={containerRef} className={`relative scroll-transition-container ${className}`}>
      {/* Neural Interface content with portal transition effects */}
      <motion.div
        style={{
          scale,
          opacity,
          rotateX,
          rotateY,
          z,
          filter: `blur(${blur}px) brightness(${brightness})`,
          width: portalWidth,
          height: portalHeight,
          position: portalPosition,
          top: 0,
          left: 0,
          zIndex: isTransitioning ? 9999 : 10,
        }}
        className={`relative transform-gpu space-transition ${isTransitioning ? 'expanding' : ''}`}
      >
        {children}
      </motion.div>

      {/* Wormhole portal background overlay */}
      <motion.div
        style={{
          opacity: wormholeOpacity,
          scale: wormholeScale,
        }}
        className="fixed inset-0 z-5 pointer-events-none"
      >
        {/* Dimensional portal gradient */}
        <div
          className="absolute inset-0"
          style={{
            background: `radial-gradient(circle at center,
              transparent 0%,
              rgba(102, 102, 255, 0.2) 20%,
              rgba(0, 68, 204, 0.4) 40%,
              rgba(26, 26, 46, 0.8) 70%,
              rgba(0, 0, 0, 0.95) 100%)`
          }}
        />
      </motion.div>

      {/* Dramatic wormhole portal effect during transition */}
      {isMounted && isTransitioning && (
        <div className="fixed inset-0 z-6 pointer-events-none">
          {/* Wormhole tunnel rings - more dramatic */}
          {Array.from({ length: 12 }, (_, i) => (
            <motion.div
              key={`wormhole-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${80 + i * 60}px`,
                height: `${80 + i * 60}px`,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                border: `3px solid rgba(102, 102, 255, ${0.8 - i * 0.06})`,
                boxShadow: `0 0 ${20 + i * 5}px rgba(102, 102, 255, ${0.6 - i * 0.04})`,
              }}
              animate={{
                scale: [0.3, 2, 4, 8],
                opacity: [1, 0.7, 0.3, 0],
                rotate: [0, 90, 180, 360],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.1,
                ease: "easeInOut",
              }}
            />
          ))}

          {/* Portal energy waves */}
          {Array.from({ length: 6 }, (_, i) => (
            <motion.div
              key={`energy-${i}`}
              className="absolute rounded-full"
              style={{
                width: `${200 + i * 100}px`,
                height: `${200 + i * 100}px`,
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                background: `radial-gradient(circle,
                  rgba(102, 102, 255, ${0.3 - i * 0.04}) 0%,
                  rgba(0, 68, 204, ${0.2 - i * 0.03}) 30%,
                  transparent 70%)`,
              }}
              animate={{
                scale: [0.5, 1.5, 3],
                opacity: [0.8, 0.4, 0],
                rotate: [0, -180, -360],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeOut",
              }}
            />
          ))}

          {/* Fast-moving stars for speed effect */}
          {starConfigs.map((star) => (
            <motion.div
              key={star.id}
              className="absolute bg-white rounded-full"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                left: `${star.left}%`,
                top: `${star.top}%`,
                boxShadow: `0 0 ${star.size * 3}px rgba(255, 255, 255, 0.9)`,
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1.5, 0],
                y: [0, -((typeof window !== 'undefined' ? window.innerHeight : 800) * star.speed * 2)],
                x: [(star.left - 50) * 2, (star.left - 50) * 4],
              }}
              transition={{
                duration: 1.5 / star.speed,
                repeat: Infinity,
                delay: star.delay,
                ease: "easeIn",
              }}
            />
          ))}

          {/* Dimensional portal effect */}
          <motion.div
            className="absolute inset-0"
            style={{
              background: `radial-gradient(circle at center,
                transparent 0%,
                rgba(102, 102, 255, 0.1) 30%,
                rgba(0, 68, 204, 0.2) 60%,
                rgba(0, 0, 0, 0.8) 100%)`,
            }}
            animate={{
              scale: [1, 1.5, 2],
              opacity: [0, 0.5, 1],
            }}
            transition={{
              duration: 2,
              ease: "easeInOut",
            }}
          />

          {/* Nebula-like background effects */}
          {nebulaConfigs.map((nebula) => (
            <motion.div
              key={`nebula-${nebula.id}`}
              className="absolute rounded-full"
              style={{
                width: `${nebula.width}px`,
                height: `${nebula.height}px`,
                left: `${nebula.left}%`,
                top: `${nebula.top}%`,
                background: `radial-gradient(circle, rgba(102, 102, 255, 0.15) 0%, rgba(0, 68, 204, 0.08) 50%, transparent 100%)`,
              }}
              animate={{
                opacity: [0.1, 0.4, 0.1],
                scale: [0.8, 1.5, 0.8],
                rotate: [0, 360],
              }}
              transition={{
                duration: nebula.duration * 0.7,
                repeat: Infinity,
                delay: nebula.delay,
              }}
            />
          ))}
        </div>
      )}

      {/* Next section preview */}
      <motion.div
        style={{
          y: nextSectionY,
          opacity: nextSectionOpacity,
        }}
        className="relative z-20"
      >
        {nextSection}
      </motion.div>
    </div>
  );
}
