'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';

interface ScrollTransitionProps {
  children: React.ReactNode;
  nextSection: React.ReactNode;
  className?: string;
}

export default function ScrollTransition({
  children,
  nextSection,
  className = ""
}: ScrollTransitionProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Smooth spring animation for the transition
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Transform values for the "falling through space" effect
  const scale = useTransform(smoothProgress, [0, 0.2, 0.5, 0.8, 1], [1, 1.1, 1.5, 2.5, 4]);
  const opacity = useTransform(smoothProgress, [0, 0.2, 0.6, 0.9, 1], [1, 1, 0.8, 0.3, 0]);
  const rotateX = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 5, 25, 60]);
  const rotateY = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [0, 8, 20, 45]);
  const z = useTransform(smoothProgress, [0, 0.2, 0.5, 0.8, 1], [0, 20, 100, 300, 800]);

  // Background transition effect - more dramatic
  const backgroundOpacity = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 0.2, 0.6, 1]);

  // Next section reveal - smoother transition
  const nextSectionY = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [200, 100, 20, 0]);
  const nextSectionOpacity = useTransform(smoothProgress, [0, 0.4, 0.8, 1], [0, 0.3, 0.8, 1]);

  // Additional space effects
  const blur = useTransform(smoothProgress, [0, 0.3, 0.7, 1], [0, 2, 8, 15]);
  const brightness = useTransform(smoothProgress, [0, 0.5, 1], [1, 1.2, 0.3]);

  // Create stable star configurations to avoid hydration mismatch
  const starConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 100 }, (_, i) => ({
      id: i,
      size: 1 + (i % 3),
      speed: 1 + (i % 3),
      left: (i * 7.3) % 100,
      top: (i * 11.7) % 100,
      delay: (i * 0.03) % 3,
    }));
  }, [isMounted]);

  const nebulaConfigs = useMemo(() => {
    if (typeof window === 'undefined' || !isMounted) return [];

    return Array.from({ length: 5 }, (_, i) => ({
      id: i,
      width: 100 + (i * 20),
      height: 100 + (i * 15),
      left: (i * 20) % 80,
      top: (i * 25) % 80,
      delay: i * 0.4,
      duration: 8 + (i * 0.8),
    }));
  }, [isMounted]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const unsubscribe = smoothProgress.on('change', (latest) => {
      setIsTransitioning(latest > 0.2 && latest < 0.8);
    });

    return () => unsubscribe();
  }, [smoothProgress]);

  return (
    <div ref={containerRef} className={`relative scroll-transition-container ${className}`}>
      {/* Main content with transition effects */}
      <motion.div
        style={{
          scale,
          opacity,
          rotateX,
          rotateY,
          z,
          filter: `blur(${blur}px) brightness(${brightness})`,
        }}
        className="relative z-10 transform-gpu space-transition"
      >
        {children}
      </motion.div>

      {/* Transition background overlay */}
      <motion.div
        style={{ opacity: backgroundOpacity }}
        className="fixed inset-0 bg-gradient-to-b from-black via-[#1a1a2e] to-[#16213e] z-5 pointer-events-none"
      />

      {/* Stars/space effect during transition */}
      {isMounted && isTransitioning && (
        <div className="fixed inset-0 z-5 pointer-events-none">
          {starConfigs.map((star) => (
            <motion.div
              key={star.id}
              className="absolute bg-white rounded-full"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                left: `${star.left}%`,
                top: `${star.top}%`,
                boxShadow: `0 0 ${star.size * 2}px rgba(255, 255, 255, 0.8)`,
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
                y: [0, -((typeof window !== 'undefined' ? window.innerHeight : 800) * star.speed)],
              }}
              transition={{
                duration: 3 / star.speed,
                repeat: Infinity,
                delay: star.delay,
                ease: "linear",
              }}
            />
          ))}

          {/* Nebula-like background effects */}
          {nebulaConfigs.map((nebula) => (
            <motion.div
              key={`nebula-${nebula.id}`}
              className="absolute rounded-full"
              style={{
                width: `${nebula.width}px`,
                height: `${nebula.height}px`,
                left: `${nebula.left}%`,
                top: `${nebula.top}%`,
                background: `radial-gradient(circle, rgba(102, 102, 255, 0.1) 0%, rgba(0, 68, 204, 0.05) 50%, transparent 100%)`,
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scale: [0.8, 1.2, 0.8],
                rotate: [0, 360],
              }}
              transition={{
                duration: nebula.duration,
                repeat: Infinity,
                delay: nebula.delay,
              }}
            />
          ))}
        </div>
      )}

      {/* Next section preview */}
      <motion.div
        style={{
          y: nextSectionY,
          opacity: nextSectionOpacity,
        }}
        className="relative z-20"
      >
        {nextSection}
      </motion.div>
    </div>
  );
}
